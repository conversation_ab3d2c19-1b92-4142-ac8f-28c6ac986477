import { spawn } from 'child_process'
import { createServer } from 'vite'
import electron from 'electron'

let electronProcess = null
let viteServer = null

// 启动 Vite 开发服务器
async function startVite() {
  try {
    viteServer = await createServer({
      // 任何有效的用户配置选项，加上 `mode` 和 `configFile`
      configFile: './vite.config.js',
      server: {
        port: 5173
      }
    })
    
    await viteServer.listen()
    console.log('Vite 开发服务器已启动在 http://localhost:5173')
    return true
  } catch (error) {
    console.error('启动 Vite 服务器失败:', error)
    return false
  }
}

// 启动 Electron
function startElectron() {
  if (electronProcess) {
    electronProcess.kill()
  }
  
  electronProcess = spawn(electron, ['.'], {
    stdio: 'inherit',
    env: {
      ...process.env,
      VITE_DEV_SERVER_URL: 'http://localhost:5173'
    }
  })
  
  electronProcess.on('close', (code) => {
    console.log(`Electron 进程退出，代码: ${code}`)
    process.exit(code)
  })
  
  electronProcess.on('error', (error) => {
    console.error('Electron 启动失败:', error)
  })
}

// 等待端口可用
function waitForPort(port, timeout = 30000) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now()
    
    function check() {
      const net = require('net')
      const socket = new net.Socket()
      
      socket.setTimeout(1000)
      socket.on('connect', () => {
        socket.destroy()
        resolve()
      })
      
      socket.on('timeout', () => {
        socket.destroy()
        if (Date.now() - startTime > timeout) {
          reject(new Error(`端口 ${port} 在 ${timeout}ms 内未可用`))
        } else {
          setTimeout(check, 1000)
        }
      })
      
      socket.on('error', () => {
        if (Date.now() - startTime > timeout) {
          reject(new Error(`端口 ${port} 在 ${timeout}ms 内未可用`))
        } else {
          setTimeout(check, 1000)
        }
      })
      
      socket.connect(port, 'localhost')
    }
    
    check()
  })
}

// 主函数
async function main() {
  try {
    console.log('正在启动开发环境...')
    
    // 启动 Vite
    const viteStarted = await startVite()
    if (!viteStarted) {
      process.exit(1)
    }
    
    // 等待 Vite 服务器完全启动
    await waitForPort(5173)
    console.log('Vite 服务器已就绪')
    
    // 启动 Electron
    console.log('正在启动 Electron...')
    startElectron()
    
  } catch (error) {
    console.error('启动失败:', error)
    process.exit(1)
  }
}

// 处理进程退出
process.on('SIGINT', () => {
  console.log('正在关闭开发环境...')
  
  if (electronProcess) {
    electronProcess.kill()
  }
  
  if (viteServer) {
    viteServer.close()
  }
  
  process.exit(0)
})

process.on('SIGTERM', () => {
  if (electronProcess) {
    electronProcess.kill()
  }
  
  if (viteServer) {
    viteServer.close()
  }
  
  process.exit(0)
})

main()
